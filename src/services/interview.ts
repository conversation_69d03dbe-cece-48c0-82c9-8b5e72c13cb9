import { ApiServiceInstance, CandidateApiServiceInstance } from '.';
import { API_ROUTES } from '@/utils/constants';

const { LIVE_PREVIEW, LIVE_EVALUATION } = API_ROUTES.PROMPT;
const { CREATE_USER_INTERVIEW } = API_ROUTES.INTERVIEW;

export interface QuestionRequest {
  role: string;
  session_id?: string | undefined;
  cv_s3_key?: string | undefined;
  difficulty: string;
  prompt_text: string;
  qa_array: {
    question: string;
    answer: string;
  }[];
  remaining_time?: number;
}

export interface QuestionResponse {
  data: string;
}
export interface CandidateVerifyRequest {
  token: string;
}

export interface CandidateVerifyResponse {
  candidate_id: string;
}

export interface AnswerSubmissionRequest {
  interviewId: string;
  questionId: string;
  answer: string;
  duration: number;
  timestamp: string;
}

export interface InterviewCreationRequest {
  candidateProfile: {
    name: string;
    position: string;
    company: string;
  };
  totalDuration: number;
}

export interface InterviewCreationResponse {
  interviewId: string;
  totalDuration: number;
  candidateProfile: {
    name: string;
    position: string;
    company: string;
  };
}

export interface PrepareInterviewRequest {
  candidate_id: string;
  job_id?: string;
}

export interface InterviewItem {
  id: string;
  status: string;
  persona_id: string;
  execution_order: number;
  time_duration: number;
}

export interface PrepareInterviewResponse {
  candidate_id: string;
  job_id: string;
  interview_items: InterviewItem[];
}

export interface EvaluationResponse {
  evaluation: string | null;
}

export interface GenerateQuestionSyncRequest {
  channel_id: string;
}

export interface GenerateQuestionSyncResponse {
  type: string;
  channel_id: string;
  chat_id: string;
  content: string;
  audio_data: string;
}

export interface CompleteInterviewRequest {
  interview_id: string;
}

export interface CompleteInterviewResponse {
  message: string;
  interview_id: string;
  status: string;
}

export const verifyCandidate = async (
  payload: CandidateVerifyRequest
): Promise<IResponseData<CandidateVerifyResponse>> => {
  return CandidateApiServiceInstance.callPostApi<CandidateVerifyResponse, CandidateVerifyRequest>(
    API_ROUTES.CANDIDATE.VERIFY,
    payload
  );
};

// Generate next question (no auth required)
export const generateQuestion = async (
  payload: QuestionRequest
): Promise<IResponseData<QuestionResponse>> => {
  return CandidateApiServiceInstance.callPostApi<QuestionResponse, QuestionRequest>(LIVE_PREVIEW, payload);
};

// Submit answer (no auth required)
export const submitAnswer = async (
  payload: AnswerSubmissionRequest
): Promise<IResponseData<unknown>> => {
  return ApiServiceInstance.callPostApi<unknown, AnswerSubmissionRequest>(LIVE_EVALUATION, payload);
};

// Create interview session
export const createInterviewSession = async (
  payload: InterviewCreationRequest
): Promise<IResponseData<InterviewCreationResponse>> => {
  return ApiServiceInstance.callPostApi<InterviewCreationResponse, InterviewCreationRequest>(
    CREATE_USER_INTERVIEW,
    payload
  );
};

// Prepare interview session (no auth required)
export const prepareInterview = async (
  payload: PrepareInterviewRequest
): Promise<IResponseData<PrepareInterviewResponse>> => {
  return CandidateApiServiceInstance.callPostApi<PrepareInterviewResponse, PrepareInterviewRequest>(
    API_ROUTES.INTERVIEW.PREPARE_INTERVIEW,
    payload
  );
};

// Run evaluation for interview
export const runEvaluation = async (
  interviewId: string
): Promise<IResponseData<EvaluationResponse>> => {
  return CandidateApiServiceInstance.callPostApi<EvaluationResponse, { interview_id: string }>(
    API_ROUTES.INTERVIEW.RUN_EVALUATION(interviewId),
    { interview_id: interviewId }
  );
};

// Generate question sync (text + audio response)
export const generateQuestionSync = async (
  payload: GenerateQuestionSyncRequest
): Promise<IResponseData<GenerateQuestionSyncResponse>> => {
  return CandidateApiServiceInstance.callPostApi<GenerateQuestionSyncResponse, GenerateQuestionSyncRequest>(
    'https://previa-ques-generator.vivasoftltd.co/api/v1/question-engine/generate-question/sync',
    payload
  );
};

// Complete interview session
export const completeInterview = async (
  interviewId: string
): Promise<IResponseData<CompleteInterviewResponse>> => {
  return ApiServiceInstance.callPatchApi<CompleteInterviewResponse, CompleteInterviewRequest>(
    API_ROUTES.INTERVIEW.COMPLETE_INTERVIEW(interviewId),
    { interview_id: interviewId }
  );
};
