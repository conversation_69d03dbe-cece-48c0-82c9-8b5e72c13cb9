import PreviaAvatar from '@/assets/icons/previa-avatar';
import { sttService } from '../services/sttService';
import { timerService } from '../services/timerService';
import {
  answerState,
  currentQuestion,
  interviewProgress,
  qaHistory,
} from '../signals/interviewSignals';
import {
  canRequestNewQuestion,
  currentPhase,
  editTimeRemaining,
  InterviewPhase,
} from '../signals/timerSignals';
import { useGenerateQuestionAPIMutation } from '@/hooks/api/use-interview';
import { InterviewItem } from '@/services/interview';
import { InterviewSessionStorage } from '@/utils/sessionStorage';
import { useSignals } from '@preact/signals-react/runtime';
import { ClockFading } from 'lucide-react';
import { useEffect } from 'react';

export function TranscriptionEditor() {
  useSignals();
  const generateQuestionMutation = useGenerateQuestionAPIMutation();

  useEffect(() => {
    if (currentPhase.value === InterviewPhase.TRANSCRIBING) {
      transcribeAnswer();
    }
  }, [currentPhase.value]);

  const transcribeAnswer = async () => {
    if (!answerState.value.recordingBlob) {
      currentPhase.value = InterviewPhase.EDITING;
      return;
    }

    answerState.value = {
      ...answerState.value,
      isTranscribing: true,
    };

    try {
      // Get channel_id and chat_id from session storage
      const channelId = InterviewSessionStorage.getChannelId();
      const chatId = InterviewSessionStorage.getChatId();

      const transcribedText = await sttService.transcribeAudio(
        answerState.value.recordingBlob as Blob,
        channelId,
        chatId
      );

      answerState.value = {
        ...answerState.value,
        transcribedText,
        editedAnswer: transcribedText,
        isTranscribing: false,
      };
      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    } catch (error) {
      console.error('Transcription failed:', error);
      answerState.value = {
        ...answerState.value,
        transcribedText: 'Transcription failed. Please type your answer.',
        editedAnswer: '',
        isTranscribing: false,
      };
      currentPhase.value = InterviewPhase.EDITING;
      timerService.startEditTimer();
    }
  };

  const handleSubmit = async () => {
    try {
      const interviewData = InterviewSessionStorage.getInterviewItems() as InterviewItem[];

      const payload = {
        channel_id: interviewData?.[0]?.id,
        last_question_answer: {
          answer: answerState.value.editedAnswer,
        },
      };

      await generateQuestionMutation.mutateAsync(payload);

      // Add to Q&A history
      qaHistory.value = [
        ...qaHistory.value,
        {
          question: currentQuestion.value.text,
          answer: answerState.value.editedAnswer,
        },
      ];

      console.log('Answer submitted successfully');
    } catch (error) {
      console.error('Failed to submit answer:', error);
    }

    // Reset answer state
    answerState.value = {
      currentAnswer: '',
      transcribedText: '',
      editedAnswer: '',
      isRecording: false,
      isTranscribing: false,
      recordingBlob: null,
    };

    // Move to next question or complete interview
    if (canRequestNewQuestion.value && !interviewProgress.value.isCompleted) {
      currentPhase.value = InterviewPhase.LOADING_QUESTION;
    } else {
      timerService.completeInterview(interviewProgress.value.completionReason || 'time_up');
    }
  };

  if (currentPhase.value === InterviewPhase.TRANSCRIBING) {
     return (
      <div className="flex items-center justify-center h-full bg-gray-50 p-8">
        <div className="flex flex-col items-center text-center max-w-md">
                    <PreviaAvatar className='size-10' />


          <h3 className="text-xl font-semibold text-gray-900 mb-3">Just a moment... we're analyzing your response</h3>

          <p className="text-gray-600 text-sm">After transcription, you'll have 30 sec to review your answer.</p>
        </div>
      </div>
    )
  }

  if (currentPhase.value === InterviewPhase.EDITING) {




    return (
      <div className='space-y-4'>
        {/* Header with timer */}
        <div className='flex items-center justify-between'>
          <h3 className='text-lg font-medium text-gray-900'>Your answer</h3>
          <div className='flex items-center gap-2 text-sm text-orange-600'>
            <ClockFading className='size-4' />
            <span>Time to Talk: 0:{editTimeRemaining.value.toString().padStart(2, '0')} min</span>
          </div>
        </div>

        {/* Answer textarea */}
        <textarea
          className='h-40 w-full resize-none rounded-md border border-gray-300 p-4 text-sm text-gray-900 focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none'
          value={answerState.value.editedAnswer}
          onChange={(e) =>
            (answerState.value = {
              ...answerState.value,
              editedAnswer: e.target.value,
            })
          }
          placeholder='Type or edit your answer here...'
          onPaste={(e) => e.preventDefault()}
          onCopy={(e) => e.preventDefault()}
          onCut={(e) => e.preventDefault()}
          onDrop={(e) => e.preventDefault()}
          onDragOver={(e) => e.preventDefault()}
        />

        {/* Submit button */}
        <button
          onClick={handleSubmit}
          disabled={generateQuestionMutation.isPending}
          className='flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-3 text-sm font-medium text-white transition hover:bg-blue-700 disabled:opacity-50'
        >
          {generateQuestionMutation.isPending ? (
            <>
              <div className='h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
              Submitting...
            </>
          ) : (
            <>
              <span>✓</span>
              Submit my answer
            </>
          )}
        </button>

        {generateQuestionMutation.isError && (
          <p className='text-center text-sm text-red-500'>
            Failed to submit answer, but continuing interview
          </p>
        )}
      </div>
    );
  }

  return null;
}
