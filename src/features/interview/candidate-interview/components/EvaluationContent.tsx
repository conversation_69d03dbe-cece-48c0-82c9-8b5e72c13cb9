import { FileText } from "lucide-react";
import ReactMarkdown from "react-markdown";
import { Card, CardContent } from "@/components/ui/card";

interface EvaluationContentProps {
  overallComment: string | null;
  isCandidateDataLoading: boolean;
}

const EvaluationContent = ({
  overallComment,
  isCandidateDataLoading,
}: EvaluationContentProps) => {
  return (
    <Card className="h-evaluation-template overflow-auto bg-white">
      <CardContent className="space-y-6 p-6">
        {isCandidateDataLoading ? (
          <div className="space-y-4">
            <div className="h-6 w-3/4 animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-full animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-5/6 animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-4/5 animate-pulse rounded bg-gray-200" />
          </div>
        ) : overallComment && overallComment.trim() ? (
          <div className="prose max-w-none">
            <ReactMarkdown>{overallComment.replace(/\\n/g, "\n")}</ReactMarkdown>
          </div>
        ) : (
          <div className="flex h-full min-h-[400px] items-center justify-center">
            <div className="text-center">
              <div className="mb-2 text-gray-400">
                <FileText className="mx-auto size-12" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">No Evaluation Available</h3>
              <p className="mt-1 text-sm text-gray-500">
                The overall evaluation comment has not been generated yet.<br/>
                Or the candidate did not attend the interview.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EvaluationContent;